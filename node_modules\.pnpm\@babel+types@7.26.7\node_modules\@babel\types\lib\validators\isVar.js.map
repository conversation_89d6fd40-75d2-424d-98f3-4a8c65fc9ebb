{"version": 3, "names": ["_index", "require", "_index2", "isVar", "node", "isVariableDeclaration", "kind", "BLOCK_SCOPED_SYMBOL"], "sources": ["../../src/validators/isVar.ts"], "sourcesContent": ["import { isVariableDeclaration } from \"./generated/index.ts\";\nimport { BLOCK_SCOPED_SYMBOL } from \"../constants/index.ts\";\nimport type * as t from \"../index.ts\";\n\n/**\n * Check if the input `node` is a variable declaration.\n */\nexport default function isVar(node: t.Node): boolean {\n  return (\n    isVariableDeclaration(node, { kind: \"var\" }) &&\n    !(\n      // @ts-expect-error document private properties\n      node[BLOCK_SCOPED_SYMBOL]\n    )\n  );\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AAMe,SAASE,KAAKA,CAACC,IAAY,EAAW;EACnD,OACE,IAAAC,4BAAqB,EAACD,IAAI,EAAE;IAAEE,IAAI,EAAE;EAAM,CAAC,CAAC,IAC5C,CAEEF,IAAI,CAACG,2BAAmB,CACzB;AAEL", "ignoreList": []}