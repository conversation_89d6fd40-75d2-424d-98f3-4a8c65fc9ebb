{"version": 3, "names": ["STATEMENT_OR_BLOCK_KEYS", "exports", "FLATTENABLE_KEYS", "FOR_INIT_KEYS", "COMMENT_KEYS", "LOGICAL_OPERATORS", "UPDATE_OPERATORS", "BOOLEAN_NUMBER_BINARY_OPERATORS", "EQUALITY_BINARY_OPERATORS", "COMPARISON_BINARY_OPERATORS", "BOOLEAN_BINARY_OPERATORS", "NUMBER_BINARY_OPERATORS", "BINARY_OPERATORS", "ASSIGNMENT_OPERATORS", "map", "op", "BOOLEAN_UNARY_OPERATORS", "NUMBER_UNARY_OPERATORS", "STRING_UNARY_OPERATORS", "UNARY_OPERATORS", "INHERIT_KEYS", "optional", "force", "BLOCK_SCOPED_SYMBOL", "Symbol", "for", "NOT_LOCAL_BINDING"], "sources": ["../../src/constants/index.ts"], "sourcesContent": ["export const STATEMENT_OR_BLOCK_KEYS = [\"consequent\", \"body\", \"alternate\"];\nexport const FLATTENABLE_KEYS = [\"body\", \"expressions\"];\nexport const FOR_INIT_KEYS = [\"left\", \"init\"];\nexport const COMMENT_KEYS = [\n  \"leadingComments\",\n  \"trailingComments\",\n  \"innerComments\",\n] as const;\n\nexport const LOGICAL_OPERATORS = [\"||\", \"&&\", \"??\"];\nexport const UPDATE_OPERATORS = [\"++\", \"--\"];\n\nexport const BOOLEAN_NUMBER_BINARY_OPERATORS = [\">\", \"<\", \">=\", \"<=\"];\nexport const EQUALITY_BINARY_OPERATORS = [\"==\", \"===\", \"!=\", \"!==\"];\nexport const COMPARISON_BINARY_OPERATORS = [\n  ...EQUALITY_BINARY_OPERATORS,\n  \"in\",\n  \"instanceof\",\n];\nexport const BOOLEAN_BINARY_OPERATORS = [\n  ...COMPARISON_BINARY_OPERATORS,\n  ...BOOLEAN_NUMBER_BINARY_OPERATORS,\n];\nexport const NUMBER_BINARY_OPERATORS = [\n  \"-\",\n  \"/\",\n  \"%\",\n  \"*\",\n  \"**\",\n  \"&\",\n  \"|\",\n  \">>\",\n  \">>>\",\n  \"<<\",\n  \"^\",\n];\nexport const BINARY_OPERATORS = [\n  \"+\",\n  ...NUMBER_BINARY_OPERATORS,\n  ...BOOLEAN_BINARY_OPERATORS,\n  \"|>\",\n];\n\nexport const ASSIGNMENT_OPERATORS = [\n  \"=\",\n  \"+=\",\n  ...NUMBER_BINARY_OPERATORS.map(op => op + \"=\"),\n  ...LOGICAL_OPERATORS.map(op => op + \"=\"),\n];\n\nexport const BOOLEAN_UNARY_OPERATORS = [\"delete\", \"!\"];\nexport const NUMBER_UNARY_OPERATORS = [\"+\", \"-\", \"~\"];\nexport const STRING_UNARY_OPERATORS = [\"typeof\"];\nexport const UNARY_OPERATORS = [\n  \"void\",\n  \"throw\",\n  ...BOOLEAN_UNARY_OPERATORS,\n  ...NUMBER_UNARY_OPERATORS,\n  ...STRING_UNARY_OPERATORS,\n];\n\nexport const INHERIT_KEYS = {\n  optional: [\"typeAnnotation\", \"typeParameters\", \"returnType\"],\n  force: [\"start\", \"loc\", \"end\"],\n} as const;\n\nexport const BLOCK_SCOPED_SYMBOL = Symbol.for(\"var used to be block scoped\");\nexport const NOT_LOCAL_BINDING = Symbol.for(\n  \"should not be considered a local binding\",\n);\n"], "mappings": ";;;;;;AAAO,MAAMA,uBAAuB,GAAAC,OAAA,CAAAD,uBAAA,GAAG,CAAC,YAAY,EAAE,MAAM,EAAE,WAAW,CAAC;AACnE,MAAME,gBAAgB,GAAAD,OAAA,CAAAC,gBAAA,GAAG,CAAC,MAAM,EAAE,aAAa,CAAC;AAChD,MAAMC,aAAa,GAAAF,OAAA,CAAAE,aAAA,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;AACtC,MAAMC,YAAY,GAAAH,OAAA,CAAAG,YAAA,GAAG,CAC1B,iBAAiB,EACjB,kBAAkB,EAClB,eAAe,CACP;AAEH,MAAMC,iBAAiB,GAAAJ,OAAA,CAAAI,iBAAA,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AAC5C,MAAMC,gBAAgB,GAAAL,OAAA,CAAAK,gBAAA,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;AAErC,MAAMC,+BAA+B,GAAAN,OAAA,CAAAM,+BAAA,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;AAC9D,MAAMC,yBAAyB,GAAAP,OAAA,CAAAO,yBAAA,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;AAC5D,MAAMC,2BAA2B,GAAAR,OAAA,CAAAQ,2BAAA,GAAG,CACzC,GAAGD,yBAAyB,EAC5B,IAAI,EACJ,YAAY,CACb;AACM,MAAME,wBAAwB,GAAAT,OAAA,CAAAS,wBAAA,GAAG,CACtC,GAAGD,2BAA2B,EAC9B,GAAGF,+BAA+B,CACnC;AACM,MAAMI,uBAAuB,GAAAV,OAAA,CAAAU,uBAAA,GAAG,CACrC,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,IAAI,EACJ,GAAG,EACH,GAAG,EACH,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,GAAG,CACJ;AACM,MAAMC,gBAAgB,GAAAX,OAAA,CAAAW,gBAAA,GAAG,CAC9B,GAAG,EACH,GAAGD,uBAAuB,EAC1B,GAAGD,wBAAwB,EAC3B,IAAI,CACL;AAEM,MAAMG,oBAAoB,GAAAZ,OAAA,CAAAY,oBAAA,GAAG,CAClC,GAAG,EACH,IAAI,EACJ,GAAGF,uBAAuB,CAACG,GAAG,CAACC,EAAE,IAAIA,EAAE,GAAG,GAAG,CAAC,EAC9C,GAAGV,iBAAiB,CAACS,GAAG,CAACC,EAAE,IAAIA,EAAE,GAAG,GAAG,CAAC,CACzC;AAEM,MAAMC,uBAAuB,GAAAf,OAAA,CAAAe,uBAAA,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC;AAC/C,MAAMC,sBAAsB,GAAAhB,OAAA,CAAAgB,sBAAA,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC9C,MAAMC,sBAAsB,GAAAjB,OAAA,CAAAiB,sBAAA,GAAG,CAAC,QAAQ,CAAC;AACzC,MAAMC,eAAe,GAAAlB,OAAA,CAAAkB,eAAA,GAAG,CAC7B,MAAM,EACN,OAAO,EACP,GAAGH,uBAAuB,EAC1B,GAAGC,sBAAsB,EACzB,GAAGC,sBAAsB,CAC1B;AAEM,MAAME,YAAY,GAAAnB,OAAA,CAAAmB,YAAA,GAAG;EAC1BC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,YAAY,CAAC;EAC5DC,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK;AAC/B,CAAU;AAEH,MAAMC,mBAAmB,GAAAtB,OAAA,CAAAsB,mBAAA,GAAGC,MAAM,CAACC,GAAG,CAAC,6BAA6B,CAAC;AACrE,MAAMC,iBAAiB,GAAAzB,OAAA,CAAAyB,iBAAA,GAAGF,MAAM,CAACC,GAAG,CACzC,0CACF,CAAC", "ignoreList": []}